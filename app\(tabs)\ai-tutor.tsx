import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, Alert, KeyboardAvoidingView, Platform, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Send, Bot, User, Sparkles, Brain, Calculator, Beaker, BookOpen, FileText } from 'lucide-react-native';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Loading from '@/components/ui/Loading';
import ChatMessage from '@/components/ui/ChatMessage';
import { colors, spacing, borderRadius, typography } from '@/constants/theme';

const { width } = Dimensions.get('window');

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  type?: 'text' | 'math' | 'code' | 'mixed';
  subject?: 'physics' | 'chemistry' | 'mathematics' | 'biology' | 'english' | 'general';
}

export default function AITutorScreen() {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hello! I'm your AI tutor for HSC Science. I can help you with Physics, Chemistry, Mathematics, Biology, and English. What would you like to learn today?",
      isUser: false,
      timestamp: new Date(),
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const quickQuestions = [
    {
      text: "Explain rotational dynamics with equations",
      subject: "physics",
      icon: Brain,
      color: colors.accent.blue,
    },
    {
      text: "Help with organic chemistry reactions",
      subject: "chemistry",
      icon: Beaker,
      color: colors.accent.green,
    },
    {
      text: "Solve calculus integration problems",
      subject: "mathematics",
      icon: Calculator,
      color: colors.accent.purple,
    },
    {
      text: "Explain human reproduction system",
      subject: "biology",
      icon: BookOpen,
      color: colors.accent.cyan,
    },
    {
      text: "Grammar rules and sentence structure",
      subject: "english",
      icon: FileText,
      color: colors.accent.orange,
    }
  ];

  useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: true });
  }, [messages]);

  const callGeminiAPI = async (prompt: string): Promise<string> => {
    try {
      const API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY;
      if (!API_KEY) {
        throw new Error('Gemini API key not found');
      }

      const enhancedPrompt = `You are an expert HSC Science tutor specializing in Maharashtra Board Class 12 Science subjects (Physics, Chemistry, Mathematics, Biology, and English).

      Please provide a comprehensive, educational response to this student question: "${prompt}"

      **IMPORTANT FORMATTING GUIDELINES:**
      - Use **Markdown formatting** for better readability
      - For mathematical equations, use LaTeX notation with $ for inline math and $$ for block equations
      - Use \`code\` for chemical formulas and technical terms
      - Use **bold** for important concepts and *italics* for emphasis
      - Create numbered lists for step-by-step solutions
      - Use bullet points for key concepts
      - Include relevant examples and formulas where applicable
      - For chemical equations, use proper subscripts and superscripts
      - For physics equations, show dimensional analysis when relevant

      **Content Guidelines:**
      - Give clear, step-by-step explanations
      - Use simple language appropriate for Class 12 students
      - Focus on conceptual understanding
      - If it's a problem, show the complete solution process
      - Include practical applications and real-world examples
      - Encourage further learning with related topics
      - Keep responses thorough but well-organized

      **Examples of good formatting:**
      - Math: "The quadratic formula is $$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$"
      - Chemistry: "Water molecule \`H₂O\` has a bent structure"
      - Physics: "Newton's second law: $$F = ma$$"

      Question: ${prompt}`;

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: enhancedPrompt
            }]
          }]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        return data.candidates[0].content.parts[0].text;
      } else {
        throw new Error('Invalid response format from Gemini API');
      }
    } catch (error) {
      console.error('Gemini API Error:', error);
      return "I apologize, but I'm having trouble connecting to my knowledge base right now. Please try again in a moment, or rephrase your question. In the meantime, you can refer to your textbooks or ask your teacher for help with this topic.";
    }
  };

  const sendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      const aiResponse = await callGeminiAPI(inputText.trim());

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: aiResponse,
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);

      // Save study session
      if (user) {
        await supabase.from('study_sessions').insert({
          user_id: user.id,
          subject: 'AI Tutoring',
          duration: 5, // Approximate 5 minutes per AI interaction
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to get response from AI tutor. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickQuestion = (question: { text: string; subject: string }) => {
    setInputText(question.text);
  };

  const renderMessage = (message: Message) => {
    const messageStyle = {
      ...styles.messageContainer,
      ...(message.isUser ? styles.userMessage : styles.aiMessage),
    };

    return (
    <Card
      key={message.id}
      variant={message.isUser ? "flat" : "elevated"}
      style={messageStyle}
    >
      <View style={styles.messageHeader}>
        <View style={[
          styles.avatarContainer,
          { backgroundColor: message.isUser ? colors.primary[100] : colors.accent.green + '20' }
        ]}>
          {message.isUser ? (
            <User size={16} color={colors.primary[500]} />
          ) : (
            <Bot size={16} color={colors.accent.green} />
          )}
        </View>
        <View style={styles.messageInfo}>
          <Text style={styles.messageSender}>
            {message.isUser ? 'You' : 'AI Tutor'}
          </Text>
          <Text style={styles.messageTime}>
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </View>
      </View>
      <Text style={[
        styles.messageText,
        message.isUser ? styles.userMessageText : styles.aiMessageText
      ]}>
        {message.text}
      </Text>
    </Card>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <Card variant="elevated" style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <View style={styles.aiIcon}>
              <Sparkles size={24} color={colors.primary[500]} />
            </View>
            <View>
              <Text style={styles.title}>AI Tutor</Text>
              <Text style={styles.subtitle}>Your personal HSC Science assistant</Text>
            </View>
          </View>
          <View style={styles.statusIndicator}>
            <View style={styles.onlineDot} />
            <Text style={styles.statusText}>Online</Text>
          </View>
        </View>
      </Card>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.chatContainer}
      >
        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
        >
          {messages.map(renderMessage)}

          {isLoading && (
            <Card
              variant="elevated"
              style={{
                ...styles.messageContainer,
                ...styles.aiMessage,
              }}
            >
              <View style={styles.messageHeader}>
                <View style={[styles.avatarContainer, { backgroundColor: colors.accent.green + '20' }]}>
                  <Bot size={16} color={colors.accent.green} />
                </View>
                <View style={styles.messageInfo}>
                  <Text style={styles.messageSender}>AI Tutor</Text>
                </View>
              </View>
              <Loading variant="dots" text="Thinking..." color={colors.accent.green} />
            </Card>
          )}
        </ScrollView>

        {/* Quick Questions */}
        {messages.length <= 2 && (
          <Card variant="elevated" style={styles.quickQuestionsContainer}>
            <Text style={styles.quickQuestionsTitle}>💡 Try asking:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.quickQuestionsScroll}>
              {quickQuestions.map((question, index) => (
                <Card
                  key={index}
                  variant="elevated"
                  onPress={() => handleQuickQuestion(question)}
                  style={[styles.quickQuestionCard, { borderLeftColor: question.color }]}
                >
                  <View style={styles.quickQuestionContent}>
                    <View style={[styles.quickQuestionIcon, { backgroundColor: question.color + '20' }]}>
                      <question.icon size={16} color={question.color} />
                    </View>
                    <Text style={styles.quickQuestionText}>{question.text}</Text>
                  </View>
                </Card>
              ))}
            </ScrollView>
          </Card>
        )}

        {/* Input Area */}
        <Card variant="elevated" style={styles.inputContainer}>
          <View style={styles.inputContent}>
            <TextInput
              style={styles.textInput}
              value={inputText}
              onChangeText={setInputText}
              placeholder="Ask me anything about HSC Science..."
              placeholderTextColor={colors.text.tertiary}
              multiline
              maxLength={500}
            />
            <Button
              title=""
              onPress={sendMessage}
              disabled={!inputText.trim() || isLoading}
              variant="gradient"
              size="sm"
              style={styles.sendButton}
              icon={<Send size={20} color={colors.text.inverse} />}
            />
          </View>
        </Card>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  header: {
    marginHorizontal: spacing.lg,
    marginTop: spacing.md,
    marginBottom: spacing.lg,
    padding: spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  aiIcon: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.xl,
    backgroundColor: colors.primary[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  title: {
    fontSize: typography.sizes['2xl'],
    fontFamily: typography.fonts.headingBold,
    color: colors.text.primary,
  },
  subtitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  onlineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.status.success,
    marginRight: spacing.xs,
  },
  statusText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.medium,
    color: colors.status.success,
  },
  chatContainer: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },
  messageContainer: {
    marginVertical: spacing.sm,
    maxWidth: '85%',
    padding: spacing.lg,
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: colors.primary[50],
    borderBottomRightRadius: borderRadius.sm,
  },
  aiMessage: {
    alignSelf: 'flex-start',
    borderBottomLeftRadius: borderRadius.sm,
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  avatarContainer: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  messageInfo: {
    flex: 1,
  },
  messageSender: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.secondary,
  },
  messageText: {
    fontSize: typography.sizes.base,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.base,
    fontFamily: typography.fonts.regular,
  },
  userMessageText: {
    color: colors.primary[700],
  },
  aiMessageText: {
    color: colors.text.primary,
  },
  messageTime: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.regular,
    color: colors.text.tertiary,
    marginTop: spacing.xs,
  },
  quickQuestionsContainer: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    padding: spacing.lg,
  },
  quickQuestionsTitle: {
    fontSize: typography.sizes.base,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  quickQuestionsScroll: {
    flexDirection: 'row',
  },
  quickQuestionButton: {
    marginRight: spacing.sm,
    minWidth: 120,
  },
  inputContainer: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    padding: spacing.lg,
  },
  inputContent: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: spacing.md,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border.light,
    borderRadius: borderRadius['2xl'],
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    fontSize: typography.sizes.base,
    fontFamily: typography.fonts.regular,
    maxHeight: 100,
    color: colors.text.primary,
    backgroundColor: colors.background.tertiary,
  },
  sendButton: {
    width: 48,
    height: 48,
    borderRadius: borderRadius['2xl'],
    padding: 0,
  },
  sendButtonDisabled: {
    opacity: 0.6,
  },
});