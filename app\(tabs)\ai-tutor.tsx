import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { <PERSON>, Bo<PERSON>, Sparkles, Brain, Calculator, Beaker, BookOpen, FileText } from 'lucide-react-native';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Loading from '@/components/ui/Loading';
import ChatMessage from '@/components/ui/ChatMessage';
import { colors, spacing, borderRadius, typography } from '@/constants/theme';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  type?: 'text' | 'math' | 'code' | 'mixed';
  subject?: 'physics' | 'chemistry' | 'mathematics' | 'biology' | 'english' | 'general';
}

export default function AITutorScreen() {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hello! I'm your AI tutor for HSC Science. I can help you with Physics, Chemistry, Mathematics, Biology, and English. What would you like to learn today?",
      isUser: false,
      timestamp: new Date(),
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const quickQuestions = [
    {
      text: "Explain rotational dynamics with equations",
      subject: "physics",
      icon: Brain,
      color: colors.accent.blue,
    },
    {
      text: "Help with organic chemistry reactions",
      subject: "chemistry",
      icon: Beaker,
      color: colors.accent.green,
    },
    {
      text: "Solve calculus integration problems",
      subject: "mathematics",
      icon: Calculator,
      color: colors.accent.purple,
    },
    {
      text: "Explain human reproduction system",
      subject: "biology",
      icon: BookOpen,
      color: colors.accent.cyan,
    },
    {
      text: "Grammar rules and sentence structure",
      subject: "english",
      icon: FileText,
      color: colors.accent.orange,
    }
  ];

  useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: true });
  }, [messages]);

  const callGeminiAPI = async (prompt: string): Promise<string> => {
    try {
      const API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY;
      if (!API_KEY) {
        throw new Error('Gemini API key not found');
      }

      const enhancedPrompt = `You are an expert HSC Science tutor specializing in Maharashtra Board Class 12 Science subjects (Physics, Chemistry, Mathematics, Biology, and English).

      Please provide a comprehensive, educational response to this student question: "${prompt}"

      **IMPORTANT FORMATTING GUIDELINES:**
      - Use **Markdown formatting** for better readability
      - For mathematical equations, use LaTeX notation with $ for inline math and $$ for block equations
      - Use \`code\` for chemical formulas and technical terms
      - Use **bold** for important concepts and *italics* for emphasis
      - Create numbered lists for step-by-step solutions
      - Use bullet points for key concepts
      - Include relevant examples and formulas where applicable
      - For chemical equations, use proper subscripts and superscripts
      - For physics equations, show dimensional analysis when relevant

      **Content Guidelines:**
      - Give clear, step-by-step explanations
      - Use simple language appropriate for Class 12 students
      - Focus on conceptual understanding
      - If it's a problem, show the complete solution process
      - Include practical applications and real-world examples
      - Encourage further learning with related topics
      - Keep responses thorough but well-organized

      **Examples of good formatting:**
      - Math: "The quadratic formula is $$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$"
      - Chemistry: "Water molecule \`H₂O\` has a bent structure"
      - Physics: "Newton's second law: $$F = ma$$"

      Question: ${prompt}`;

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: enhancedPrompt
            }]
          }]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        return data.candidates[0].content.parts[0].text;
      } else {
        throw new Error('Invalid response format from Gemini API');
      }
    } catch (error) {
      console.error('Gemini API Error:', error);
      return "I apologize, but I'm having trouble connecting to my knowledge base right now. Please try again in a moment, or rephrase your question. In the meantime, you can refer to your textbooks or ask your teacher for help with this topic.";
    }
  };

  const sendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      const aiResponse = await callGeminiAPI(inputText.trim());

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: aiResponse,
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);

      // Save study session
      if (user) {
        await supabase.from('study_sessions').insert({
          user_id: user.id,
          subject: 'AI Tutoring',
          duration: 5, // Approximate 5 minutes per AI interaction
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to get response from AI tutor. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickQuestion = (question: { text: string; subject: string }) => {
    setInputText(question.text);
  };

  const handleRegenerate = async () => {
    if (messages.length > 0) {
      const lastUserMessage = [...messages].reverse().find(m => m.isUser);
      if (lastUserMessage) {
        // Remove the last AI response
        setMessages(prev => prev.slice(0, -1));
        // Regenerate response
        setIsLoading(true);
        try {
          const aiResponse = await callGeminiAPI(lastUserMessage.text);
          const aiMessage: Message = {
            id: Date.now().toString(),
            text: aiResponse,
            isUser: false,
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, aiMessage]);
        } catch (error) {
          console.error('Error regenerating message:', error);
        } finally {
          setIsLoading(false);
        }
      }
    }
  };

  const handleFeedback = async (messageId: string, isPositive: boolean) => {
    // Here you could save feedback to your database
    console.log(`Feedback for message ${messageId}: ${isPositive ? 'positive' : 'negative'}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <Card variant="elevated" style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <View style={styles.aiIcon}>
              <Sparkles size={24} color={colors.primary[500]} />
            </View>
            <View>
              <Text style={styles.title}>AI Tutor</Text>
              <Text style={styles.subtitle}>Your personal HSC Science assistant</Text>
            </View>
          </View>
          <View style={styles.statusIndicator}>
            <View style={styles.onlineDot} />
            <Text style={styles.statusText}>Online</Text>
          </View>
        </View>
      </Card>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.chatContainer}
      >
        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
        >
          {messages.map((message) => (
            <ChatMessage
              key={message.id}
              message={message}
              onRegenerate={!message.isUser ? handleRegenerate : undefined}
              onFeedback={!message.isUser ? handleFeedback : undefined}
            />
          ))}

          {isLoading && (
            <Card
              variant="elevated"
              style={styles.loadingMessage}
            >
              <View style={styles.loadingContent}>
                <View style={styles.loadingAvatar}>
                  <Bot size={16} color={colors.accent.green} />
                </View>
                <Loading variant="dots" text="AI is thinking..." color={colors.accent.green} />
              </View>
            </Card>
          )}
        </ScrollView>

        {/* Quick Questions */}
        {messages.length <= 2 && (
          <Card variant="elevated" style={styles.quickQuestionsContainer}>
            <Text style={styles.quickQuestionsTitle}>💡 Try asking:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.quickQuestionsScroll}>
              {quickQuestions.map((question, index) => {
                const cardStyle = {
                  ...styles.quickQuestionCard,
                  borderLeftColor: question.color,
                };

                return (
                <Card
                  key={index}
                  variant="elevated"
                  onPress={() => handleQuickQuestion(question)}
                  style={cardStyle}
                >
                  <View style={styles.quickQuestionContent}>
                    <View style={[styles.quickQuestionIcon, { backgroundColor: question.color + '20' }]}>
                      <question.icon size={16} color={question.color} />
                    </View>
                    <Text style={styles.quickQuestionText}>{question.text}</Text>
                  </View>
                </Card>
                );
              })}
            </ScrollView>
          </Card>
        )}

        {/* Input Area */}
        <Card variant="elevated" style={styles.inputContainer}>
          <View style={styles.inputContent}>
            <View style={styles.inputWrapper}>
              <TextInput
                style={styles.textInput}
                value={inputText}
                onChangeText={setInputText}
                placeholder="Ask about equations, formulas, concepts, or problems..."
                placeholderTextColor={colors.text.tertiary}
                multiline
                maxLength={1000}
                textAlignVertical="top"
                returnKeyType="send"
                onSubmitEditing={sendMessage}
              />
              <Text style={styles.characterCount}>
                {inputText.length}/1000
              </Text>
            </View>
            <Button
              title=""
              onPress={sendMessage}
              disabled={!inputText.trim() || isLoading}
              variant="gradient"
              size="sm"
              style={styles.sendButton}
              icon={<Send size={20} color={colors.text.inverse} />}
            />
          </View>
        </Card>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  header: {
    marginHorizontal: spacing.lg,
    marginTop: spacing.md,
    marginBottom: spacing.lg,
    padding: spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  aiIcon: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.xl,
    backgroundColor: colors.primary[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  title: {
    fontSize: typography.sizes['2xl'],
    fontFamily: typography.fonts.headingBold,
    color: colors.text.primary,
  },
  subtitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  onlineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.status.success,
    marginRight: spacing.xs,
  },
  statusText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.medium,
    color: colors.status.success,
  },
  chatContainer: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },

  quickQuestionsContainer: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    padding: spacing.lg,
  },
  quickQuestionsTitle: {
    fontSize: typography.sizes.base,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  quickQuestionsScroll: {
    flexDirection: 'row',
  },
  quickQuestionCard: {
    marginRight: spacing.md,
    minWidth: 180,
    maxWidth: 220,
    padding: spacing.md,
    borderLeftWidth: 3,
  },
  quickQuestionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  quickQuestionIcon: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickQuestionText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.medium,
    color: colors.text.primary,
    flex: 1,
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
  },
  loadingMessage: {
    alignSelf: 'flex-start',
    marginVertical: spacing.sm,
    maxWidth: '85%',
    padding: spacing.lg,
  },
  loadingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  loadingAvatar: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.accent.green + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
  inputContainer: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    padding: spacing.lg,
  },
  inputContent: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: spacing.md,
  },
  inputWrapper: {
    flex: 1,
    position: 'relative',
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.border.light,
    borderRadius: borderRadius['2xl'],
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    paddingBottom: spacing.lg + spacing.sm, // Extra space for character count
    fontSize: typography.sizes.base,
    fontFamily: typography.fonts.regular,
    minHeight: 48,
    maxHeight: 120,
    color: colors.text.primary,
    backgroundColor: colors.background.tertiary,
  },
  characterCount: {
    position: 'absolute',
    bottom: spacing.xs,
    right: spacing.md,
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.regular,
    color: colors.text.tertiary,
  },
  sendButton: {
    width: 48,
    height: 48,
    borderRadius: borderRadius['2xl'],
    padding: 0,
  },
  sendButtonDisabled: {
    opacity: 0.6,
  },
});