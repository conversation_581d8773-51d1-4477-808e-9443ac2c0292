import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Search, ChevronRight, BookOpen, Users, Clock, Star, TrendingUp } from 'lucide-react-native';
import { router } from 'expo-router';
import { useStudyProgress } from '@/hooks/useStudyProgress';
import Card from '@/components/ui/Card';
import { colors, spacing, borderRadius, shadows, typography } from '@/constants/theme';

export default function SubjectsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const { progress, updateProgress } = useStudyProgress();

  const subjects = [
    {
      id: 'physics',
      name: 'Physics',
      color: '#3B82F6',
      bgColor: '#EEF2FF',
      icon: '⚡',
      totalTopics: 12,
      difficulty: 'Advanced',
      chapters: [
        'Rotational Dynamics',
        'Mechanical Properties of Fluids',
        'Kinetic Theory of Gases & Radiation',
        'Electrostatics',
        'Current Electricity',
        'Magnetic Effects of Current',
        'Electromagnetic Induction',
        'Electromagnetic Waves',
        'Ray Optics & Wave Optics',
        'Dual Nature of Matter',
        'Atoms and Nuclei',
        'Electronic Devices'
      ]
    },
    {
      id: 'chemistry',
      name: 'Chemistry',
      color: '#10B981',
      bgColor: '#F0FDF4',
      icon: '🧪',
      totalTopics: 16,
      difficulty: 'Intermediate',
      chapters: [
        'The Solid State',
        'Solutions and Colligative Properties',
        'Chemical Thermodynamics',
        'Electrochemistry',
        'Chemical Kinetics',
        'Surface Chemistry',
        'p-Block Elements',
        'd- and f-Block Elements',
        'Coordination Compounds',
        'Haloalkanes and Haloarenes',
        'Alcohols, Phenols, and Ethers',
        'Aldehydes, Ketones, and Carboxylic Acids',
        'Organic Compounds Containing Nitrogen',
        'Biomolecules',
        'Polymers',
        'Chemistry in Everyday Life'
      ]
    },
    {
      id: 'mathematics',
      name: 'Mathematics',
      color: '#F59E0B',
      bgColor: '#FEF3C7',
      icon: '📐',
      totalTopics: 18,
      difficulty: 'Advanced',
      chapters: [
        'Mathematical Logic',
        'Matrices',
        'Trigonometric Functions',
        'Pair of Straight Lines',
        'Circle & Conics',
        'Vectors',
        'Three-Dimensional Geometry',
        'Linear Programming Problems',
        'Continuity',
        'Differentiation',
        'Application of Derivatives',
        'Integration',
        'Application of Definite Integral',
        'Differential Equations',
        'Statistics',
        'Probability Distribution',
        'Bernoulli Trials',
        'Binomial Distribution'
      ]
    },
    {
      id: 'biology',
      name: 'Biology',
      color: '#8B5CF6',
      bgColor: '#F3F4F6',
      icon: '🧬',
      totalTopics: 14,
      difficulty: 'Intermediate',
      chapters: [
        'Reproduction in Lower and Higher Plants',
        'Plant Water Relations',
        'Photosynthesis and Respiration',
        'Genetics and Evolution',
        'Biotechnology',
        'Microbes in Human Welfare',
        'Organisms and Environment',
        'Human Reproduction',
        'Origin and Evolution of Life',
        'Chromosomal Basis of Inheritance',
        'Human Health & Diseases',
        'Human Physiology',
        'Genetic Engineering',
        'Animal Husbandry'
      ]
    },
    {
      id: 'english',
      name: 'English',
      color: '#06B6D4',
      bgColor: '#ECFEFF',
      icon: '📚',
      totalTopics: 8,
      difficulty: 'Beginner',
      chapters: [
        'Prose and Poetry',
        'Grammar Advanced',
        'Writing Skills',
        'Reading Comprehension',
        'Novel Study',
        'Creative Writing',
        'Communication Skills',
        'Literature Analysis'
      ]
    }
  ];

  const getSubjectProgress = (subjectId: string) => {
    const subjectProgress = progress.filter(p => p.subject.toLowerCase() === subjectId);
    const completedTopics = subjectProgress.filter(p => p.completed).length;
    const totalProgress = subjectProgress.length > 0
      ? Math.round(subjectProgress.reduce((sum, p) => sum + p.progress, 0) / subjectProgress.length)
      : 0;

    return { completedTopics, totalProgress };
  };

  const filteredSubjects = subjects.filter(subject =>
    subject.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    subject.chapters.some(chapter =>
      chapter.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const handleSubjectPress = (subject: any) => {
    router.push(`/subject-detail?id=${subject.id}&name=${subject.name}&color=${encodeURIComponent(subject.color)}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>HSC Science Subjects</Text>
        <Text style={styles.subtitle}>Master all subjects for board exams</Text>
      </View>

      {/* Search Bar */}
      <Card variant="elevated" style={styles.searchContainer}>
        <View style={styles.searchContent}>
          <Search size={20} color={colors.text.tertiary} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search subjects or topics..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={colors.text.tertiary}
          />
        </View>
      </Card>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
        {filteredSubjects.map((subject) => {
          const { completedTopics, totalProgress } = getSubjectProgress(subject.id);

          return (
            <Card
              key={subject.id}
              variant="elevated"
              onPress={() => handleSubjectPress(subject)}
              style={[styles.subjectCard, { borderLeftColor: subject.color, borderLeftWidth: 4 }]}
            >
              <View style={styles.subjectHeader}>
                <View style={styles.subjectInfo}>
                  <View style={[styles.subjectIconContainer, { backgroundColor: subject.bgColor }]}>
                    <Text style={styles.subjectIcon}>{subject.icon}</Text>
                  </View>
                  <View style={styles.subjectDetails}>
                    <Text style={styles.subjectName}>{subject.name}</Text>
                    <Text style={styles.subjectStats}>
                      {completedTopics}/{subject.totalTopics} topics • {subject.difficulty}
                    </Text>
                  </View>
                </View>
                <View style={styles.subjectActions}>
                  <View style={[styles.difficultyBadge, { backgroundColor: subject.color + '20' }]}>
                    <Text style={[styles.difficultyText, { color: subject.color }]}>
                      {subject.difficulty}
                    </Text>
                  </View>
                  <ChevronRight size={20} color={colors.text.tertiary} />
                </View>
              </View>

              {/* Progress Bar */}
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      { width: `${totalProgress}%`, backgroundColor: subject.color }
                    ]}
                  />
                </View>
                <Text style={[styles.progressText, { color: subject.color }]}>{totalProgress}%</Text>
              </View>

              {/* Next Topic */}
              <View style={styles.nextTopicContainer}>
                <View style={[styles.nextTopicIcon, { backgroundColor: subject.color + '20' }]}>
                  <BookOpen size={16} color={subject.color} />
                </View>
                <Text style={styles.nextTopicText}>
                  Next: {subject.chapters[completedTopics] || 'All completed!'}
                </Text>
              </View>

              {/* Quick Stats */}
              <View style={styles.quickStats}>
                <View style={styles.stat}>
                  <Clock size={14} color={colors.text.tertiary} />
                  <Text style={styles.statText}>2h avg</Text>
                </View>
                <View style={styles.stat}>
                  <Users size={14} color={colors.text.tertiary} />
                  <Text style={styles.statText}>12k students</Text>
                </View>
                <View style={styles.stat}>
                  <Star size={14} color={colors.accent.yellow} />
                  <Text style={styles.statText}>4.8</Text>
                </View>
                <View style={styles.stat}>
                  <TrendingUp size={14} color={colors.status.success} />
                  <Text style={styles.statText}>+{Math.floor(Math.random() * 10)}%</Text>
                </View>
              </View>
            </Card>
          );
        })}

        {/* Study Tips Card */}
        <Card
          variant="gradient"
          gradientColors={colors.gradients.warm}
          style={styles.tipsCard}
        >
          <View style={styles.tipsHeader}>
            <Text style={styles.tipsIcon}>💡</Text>
            <Text style={styles.tipsTitle}>Study Tips</Text>
          </View>
          <Text style={styles.tipsText}>
            Focus on understanding concepts rather than memorization. Practice regularly and use AI tutor for doubts!
          </Text>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  header: {
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    paddingBottom: spacing['2xl'],
  },
  title: {
    fontSize: typography.sizes['4xl'],
    fontFamily: typography.fonts.headingBold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.regular,
    color: colors.text.secondary,
  },
  searchContainer: {
    marginHorizontal: spacing.xl,
    marginBottom: spacing.xl,
    padding: 0,
  },
  searchContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  searchIcon: {
    marginRight: spacing.md,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.regular,
    color: colors.text.primary,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: spacing.xl,
  },
  subjectCard: {
    marginBottom: spacing.lg,
    padding: spacing.xl,
  },
  subjectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  subjectInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  subjectIconContainer: {
    width: 64,
    height: 64,
    borderRadius: borderRadius.xl,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.lg,
  },
  subjectIcon: {
    fontSize: 32,
  },
  subjectDetails: {
    flex: 1,
  },
  subjectName: {
    fontSize: typography.sizes['2xl'],
    fontFamily: typography.fonts.headingSemiBold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  subjectStats: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.text.secondary,
  },
  subjectActions: {
    alignItems: 'flex-end',
    gap: spacing.sm,
  },
  difficultyBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
  },
  difficultyText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.semiBold,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  progressBar: {
    flex: 1,
    height: 10,
    backgroundColor: colors.background.tertiary,
    borderRadius: borderRadius.full,
    marginRight: spacing.md,
  },
  progressFill: {
    height: '100%',
    borderRadius: borderRadius.full,
  },
  progressText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.semiBold,
    minWidth: 40,
    textAlign: 'right',
  },
  nextTopicContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  nextTopicIcon: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  nextTopicText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.medium,
    color: colors.text.primary,
    flex: 1,
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border.light,
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  statText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.medium,
    color: colors.text.secondary,
    marginLeft: spacing.xs,
  },
  tipsCard: {
    marginBottom: spacing['2xl'],
    padding: spacing.xl,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  tipsIcon: {
    fontSize: 24,
    marginRight: spacing.sm,
  },
  tipsTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.inverse,
  },
  tipsText: {
    fontSize: typography.sizes.base,
    fontFamily: typography.fonts.regular,
    color: colors.text.inverse,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.base,
    opacity: 0.9,
  },
});