import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Markdown from 'react-native-markdown-display';
import { WebView } from 'react-native-webview';
import { colors, spacing, borderRadius, typography } from '@/constants/theme';

const { width } = Dimensions.get('window');

interface MarkdownRendererProps {
  content: string;
  style?: any;
}

export default function MarkdownRenderer({ content, style }: MarkdownRendererProps) {
  // Enhanced content processing for better math and formatting
  const processContent = (text: string) => {
    // Convert LaTeX-style math delimiters to proper format
    let processedText = text
      // Inline math: $...$ or \(...\)
      .replace(/\$([^$]+)\$/g, '<math>$1</math>')
      .replace(/\\\\?\(([^)]+)\\\\?\)/g, '<math>$1</math>')
      // Block math: $$...$$ or \[...\]
      .replace(/\$\$([^$]+)\$\$/g, '<blockmath>$1</blockmath>')
      .replace(/\\\\?\[([^\]]+)\\\\?\]/g, '<blockmath>$1</blockmath>')
      // Chemical formulas
      .replace(/([A-Z][a-z]?)(\d+)/g, '$1<sub>$2</sub>')
      // Superscripts for powers
      .replace(/\^(\d+)/g, '<sup>$1</sup>')
      // Common physics/chemistry symbols
      .replace(/α/g, '&alpha;')
      .replace(/β/g, '&beta;')
      .replace(/γ/g, '&gamma;')
      .replace(/δ/g, '&delta;')
      .replace(/ε/g, '&epsilon;')
      .replace(/θ/g, '&theta;')
      .replace(/λ/g, '&lambda;')
      .replace(/μ/g, '&mu;')
      .replace(/π/g, '&pi;')
      .replace(/ρ/g, '&rho;')
      .replace(/σ/g, '&sigma;')
      .replace(/τ/g, '&tau;')
      .replace(/φ/g, '&phi;')
      .replace(/ω/g, '&omega;')
      .replace(/Δ/g, '&Delta;')
      .replace(/Ω/g, '&Omega;');

    return processedText;
  };

  // Check if content contains math expressions
  const hasMath = content.includes('$') || content.includes('\\(') || content.includes('\\[');

  // If content has math, use WebView for proper rendering
  if (hasMath) {
    const mathHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
        <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
        <script>
          window.MathJax = {
            tex: {
              inlineMath: [['$', '$'], ['\\\\(', '\\\\)']],
              displayMath: [['$$', '$$'], ['\\\\[', '\\\\]']],
              processEscapes: true,
              processEnvironments: true
            },
            options: {
              skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
          };
        </script>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 16px;
            line-height: 1.6;
            color: ${colors.text.primary};
            background-color: transparent;
            margin: 0;
            padding: 16px;
          }
          .math-container {
            margin: 8px 0;
            text-align: center;
          }
          .inline-math {
            display: inline;
          }
          code {
            background-color: ${colors.background.tertiary};
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
          }
          pre {
            background-color: ${colors.background.tertiary};
            padding: 12px;
            border-radius: 8px;
            overflow-x: auto;
          }
          h1, h2, h3 {
            color: ${colors.primary[600]};
            margin-top: 20px;
            margin-bottom: 10px;
          }
          strong {
            color: ${colors.text.primary};
            font-weight: 600;
          }
          em {
            color: ${colors.text.secondary};
          }
          ul, ol {
            padding-left: 20px;
          }
          li {
            margin-bottom: 4px;
          }
          blockquote {
            border-left: 4px solid ${colors.primary[500]};
            margin: 16px 0;
            padding-left: 16px;
            color: ${colors.text.secondary};
            font-style: italic;
          }
          table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
          }
          th, td {
            border: 1px solid ${colors.border.light};
            padding: 8px 12px;
            text-align: left;
          }
          th {
            background-color: ${colors.background.tertiary};
            font-weight: 600;
          }
        </style>
      </head>
      <body>
        ${processContent(content)}
      </body>
      </html>
    `;

    return (
      <View style={[styles.webViewContainer, style]}>
        <WebView
          source={{ html: mathHTML }}
          style={styles.webView}
          scrollEnabled={false}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          onMessage={() => {}}
          injectedJavaScript={`
            setTimeout(() => {
              window.ReactNativeWebView.postMessage(document.body.scrollHeight);
            }, 1000);
          `}
        />
      </View>
    );
  }

  // For regular markdown without math
  const markdownStyles = {
    body: {
      fontSize: typography.sizes.base,
      lineHeight: typography.lineHeights.relaxed * typography.sizes.base,
      color: colors.text.primary,
      fontFamily: typography.fonts.regular,
    },
    heading1: {
      fontSize: typography.sizes['2xl'],
      fontFamily: typography.fonts.headingBold,
      color: colors.primary[600],
      marginTop: spacing.lg,
      marginBottom: spacing.md,
    },
    heading2: {
      fontSize: typography.sizes.xl,
      fontFamily: typography.fonts.headingSemiBold,
      color: colors.primary[600],
      marginTop: spacing.lg,
      marginBottom: spacing.sm,
    },
    heading3: {
      fontSize: typography.sizes.lg,
      fontFamily: typography.fonts.semiBold,
      color: colors.primary[600],
      marginTop: spacing.md,
      marginBottom: spacing.sm,
    },
    paragraph: {
      marginBottom: spacing.md,
      fontSize: typography.sizes.base,
      lineHeight: typography.lineHeights.relaxed * typography.sizes.base,
    },
    strong: {
      fontFamily: typography.fonts.semiBold,
      color: colors.text.primary,
    },
    em: {
      fontStyle: 'italic',
      color: colors.text.secondary,
    },
    code_inline: {
      backgroundColor: colors.background.tertiary,
      color: colors.primary[700],
      paddingHorizontal: spacing.xs,
      paddingVertical: 2,
      borderRadius: borderRadius.sm,
      fontFamily: 'Courier New',
      fontSize: typography.sizes.sm,
    },
    code_block: {
      backgroundColor: colors.background.tertiary,
      padding: spacing.md,
      borderRadius: borderRadius.lg,
      marginVertical: spacing.md,
    },
    fence: {
      backgroundColor: colors.background.tertiary,
      padding: spacing.md,
      borderRadius: borderRadius.lg,
      marginVertical: spacing.md,
    },
    blockquote: {
      backgroundColor: colors.primary[50],
      borderLeftWidth: 4,
      borderLeftColor: colors.primary[500],
      paddingLeft: spacing.md,
      paddingVertical: spacing.sm,
      marginVertical: spacing.md,
      fontStyle: 'italic',
    },
    list_item: {
      marginBottom: spacing.xs,
    },
    bullet_list: {
      marginVertical: spacing.sm,
    },
    ordered_list: {
      marginVertical: spacing.sm,
    },
    table: {
      borderWidth: 1,
      borderColor: colors.border.light,
      borderRadius: borderRadius.md,
      marginVertical: spacing.md,
    },
    thead: {
      backgroundColor: colors.background.tertiary,
    },
    th: {
      padding: spacing.sm,
      fontFamily: typography.fonts.semiBold,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.light,
    },
    td: {
      padding: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.light,
    },
    link: {
      color: colors.primary[600],
      textDecorationLine: 'underline',
    },
  };

  return (
    <View style={[styles.container, style]}>
      <Markdown style={markdownStyles}>
        {processContent(content)}
      </Markdown>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webViewContainer: {
    minHeight: 100,
    backgroundColor: 'transparent',
  },
  webView: {
    backgroundColor: 'transparent',
    flex: 1,
  },
});
