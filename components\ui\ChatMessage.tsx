import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Dimensions } from 'react-native';
import { Bo<PERSON>, User, Copy, ThumbsUp, ThumbsDown, RotateCcw, Volume2 } from 'lucide-react-native';
import * as Clipboard from 'expo-clipboard';
import * as Speech from 'expo-speech';
import Card from './Card';
import MarkdownRenderer from './MarkdownRenderer';
import { colors, spacing, borderRadius, typography } from '@/constants/theme';

const { width } = Dimensions.get('window');

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  type?: 'text' | 'math' | 'code' | 'mixed';
}

interface ChatMessageProps {
  message: Message;
  onRegenerate?: () => void;
  onFeedback?: (messageId: string, isPositive: boolean) => void;
}

export default function ChatMessage({ message, onRegenerate, onFeedback }: ChatMessageProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showActions, setShowActions] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);

  const maxLength = 300;
  const shouldTruncate = message.text.length > maxLength && !isExpanded;
  const displayText = shouldTruncate ? message.text.substring(0, maxLength) + '...' : message.text;

  // Determine message type based on content
  const getMessageType = (text: string): 'text' | 'math' | 'code' | 'mixed' => {
    const hasMath = text.includes('$') || text.includes('\\(') || text.includes('\\[');
    const hasCode = text.includes('```') || text.includes('`');
    
    if (hasMath && hasCode) return 'mixed';
    if (hasMath) return 'math';
    if (hasCode) return 'code';
    return 'text';
  };

  const messageType = getMessageType(message.text);

  const handleCopy = async () => {
    await Clipboard.setStringAsync(message.text);
    // Could add a toast notification here
  };

  const handleSpeak = async () => {
    if (isSpeaking) {
      Speech.stop();
      setIsSpeaking(false);
    } else {
      setIsSpeaking(true);
      // Clean text for speech (remove markdown and math)
      const cleanText = message.text
        .replace(/\$[^$]+\$/g, 'mathematical expression')
        .replace(/\$\$[^$]+\$\$/g, 'mathematical equation')
        .replace(/```[^`]+```/g, 'code block')
        .replace(/`[^`]+`/g, 'code')
        .replace(/[#*_~]/g, '')
        .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');

      Speech.speak(cleanText, {
        onDone: () => setIsSpeaking(false),
        onError: () => setIsSpeaking(false),
        rate: 0.8,
        pitch: 1.0,
      });
    }
  };

  const messageStyle = {
    ...styles.messageContainer,
    ...(message.isUser ? styles.userMessage : styles.aiMessage),
    maxWidth: width * 0.85,
  };

  return (
    <Card
      variant={message.isUser ? "flat" : "elevated"}
      style={messageStyle}
      onPress={() => setShowActions(!showActions)}
    >
      {/* Message Header */}
      <View style={styles.messageHeader}>
        <View style={[
          styles.avatarContainer,
          { backgroundColor: message.isUser ? colors.primary[100] : colors.accent.green + '20' }
        ]}>
          {message.isUser ? (
            <User size={16} color={colors.primary[500]} />
          ) : (
            <Bot size={16} color={colors.accent.green} />
          )}
        </View>
        <View style={styles.messageInfo}>
          <View style={styles.senderRow}>
            <Text style={styles.messageSender}>
              {message.isUser ? 'You' : 'AI Tutor'}
            </Text>
            {messageType !== 'text' && (
              <View style={[styles.typeBadge, { backgroundColor: getTypeBadgeColor(messageType) }]}>
                <Text style={styles.typeText}>{messageType.toUpperCase()}</Text>
              </View>
            )}
          </View>
          <Text style={styles.messageTime}>
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </View>
      </View>

      {/* Message Content */}
      <View style={styles.messageContent}>
        {message.isUser ? (
          <Text style={styles.userMessageText}>
            {displayText}
          </Text>
        ) : (
          <MarkdownRenderer 
            content={displayText}
            style={styles.markdownContent}
          />
        )}
        
        {shouldTruncate && (
          <TouchableOpacity 
            style={styles.expandButton}
            onPress={() => setIsExpanded(true)}
          >
            <Text style={styles.expandText}>Show more</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Message Actions */}
      {showActions && !message.isUser && (
        <Animated.View style={styles.actionsContainer}>
          <TouchableOpacity style={styles.actionButton} onPress={handleCopy}>
            <Copy size={16} color={colors.text.secondary} />
            <Text style={styles.actionText}>Copy</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleSpeak}>
            <Volume2 size={16} color={isSpeaking ? colors.primary[500] : colors.text.secondary} />
            <Text style={[styles.actionText, isSpeaking && { color: colors.primary[500] }]}>
              {isSpeaking ? 'Stop' : 'Speak'}
            </Text>
          </TouchableOpacity>
          
          {onRegenerate && (
            <TouchableOpacity style={styles.actionButton} onPress={onRegenerate}>
              <RotateCcw size={16} color={colors.text.secondary} />
              <Text style={styles.actionText}>Regenerate</Text>
            </TouchableOpacity>
          )}
          
          {onFeedback && (
            <View style={styles.feedbackContainer}>
              <TouchableOpacity 
                style={styles.actionButton} 
                onPress={() => onFeedback(message.id, true)}
              >
                <ThumbsUp size={16} color={colors.status.success} />
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.actionButton} 
                onPress={() => onFeedback(message.id, false)}
              >
                <ThumbsDown size={16} color={colors.status.error} />
              </TouchableOpacity>
            </View>
          )}
        </Animated.View>
      )}
    </Card>
  );
}

const getTypeBadgeColor = (type: string) => {
  switch (type) {
    case 'math': return colors.accent.blue + '20';
    case 'code': return colors.accent.green + '20';
    case 'mixed': return colors.accent.purple + '20';
    default: return colors.background.tertiary;
  }
};

const styles = StyleSheet.create({
  messageContainer: {
    marginVertical: spacing.sm,
    padding: spacing.lg,
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: colors.primary[50],
    borderBottomRightRadius: borderRadius.sm,
  },
  aiMessage: {
    alignSelf: 'flex-start',
    borderBottomLeftRadius: borderRadius.sm,
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  avatarContainer: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  messageInfo: {
    flex: 1,
  },
  senderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  messageSender: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.secondary,
  },
  typeBadge: {
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: borderRadius.sm,
  },
  typeText: {
    fontSize: 8,
    fontFamily: typography.fonts.bold,
    color: colors.text.secondary,
  },
  messageTime: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.regular,
    color: colors.text.tertiary,
    marginTop: spacing.xs,
  },
  messageContent: {
    flex: 1,
  },
  userMessageText: {
    fontSize: typography.sizes.base,
    lineHeight: typography.lineHeights.relaxed * typography.sizes.base,
    fontFamily: typography.fonts.regular,
    color: colors.primary[700],
  },
  markdownContent: {
    flex: 1,
  },
  expandButton: {
    marginTop: spacing.sm,
    alignSelf: 'flex-start',
  },
  expandText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.semiBold,
    color: colors.primary[600],
  },
  actionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border.light,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    backgroundColor: colors.background.tertiary,
    borderRadius: borderRadius.md,
  },
  actionText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.medium,
    color: colors.text.secondary,
  },
  feedbackContainer: {
    flexDirection: 'row',
    gap: spacing.xs,
  },
});
