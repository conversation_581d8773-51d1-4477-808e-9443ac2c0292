import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Animated,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { BookOpen, Brain, Target, Trophy, ChevronRight, Sparkles } from 'lucide-react-native';
import { colors, spacing, borderRadius, typography } from '@/constants/theme';

const { width, height } = Dimensions.get('window');

interface WelcomeScreenProps {
  onGetStarted: () => void;
}

export default function WelcomeScreen({ onGetStarted }: WelcomeScreenProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const features = [
    {
      icon: Brain,
      title: 'AI-Powered Learning',
      description: 'Get personalized tutoring with advanced AI technology',
      color: colors.accent.purple,
    },
    {
      icon: Target,
      title: 'Practice Tests',
      description: 'Master HSC Science with interactive quizzes and tests',
      color: colors.accent.green,
    },
    {
      icon: Trophy,
      title: 'Track Progress',
      description: 'Monitor your learning journey and celebrate achievements',
      color: colors.accent.yellow,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />
      <LinearGradient
        colors={colors.gradients.primary}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Background Decorations */}
        <View style={styles.backgroundDecorations}>
          <Animated.View 
            style={[
              styles.floatingElement,
              styles.element1,
              { opacity: fadeAnim }
            ]}
          >
            <Sparkles size={24} color="rgba(255, 255, 255, 0.3)" />
          </Animated.View>
          <Animated.View 
            style={[
              styles.floatingElement,
              styles.element2,
              { opacity: fadeAnim }
            ]}
          >
            <BookOpen size={32} color="rgba(255, 255, 255, 0.2)" />
          </Animated.View>
          <Animated.View 
            style={[
              styles.floatingElement,
              styles.element3,
              { opacity: fadeAnim }
            ]}
          >
            <Brain size={28} color="rgba(255, 255, 255, 0.25)" />
          </Animated.View>
        </View>

        <Animated.View 
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ]
            }
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <LinearGradient
                colors={['rgba(255, 255, 255, 0.3)', 'rgba(255, 255, 255, 0.1)']}
                style={styles.logoGradient}
              >
                <BookOpen size={48} color={colors.text.inverse} />
              </LinearGradient>
            </View>
            <Text style={styles.title}>HSC Science Hub</Text>
            <Text style={styles.subtitle}>
              Master Maharashtra HSC Science with AI-powered learning platform
            </Text>
          </View>

          {/* Features */}
          <View style={styles.featuresContainer}>
            {features.map((feature, index) => (
              <Animated.View
                key={index}
                style={[
                  styles.featureCard,
                  {
                    opacity: fadeAnim,
                    transform: [{
                      translateY: slideAnim.interpolate({
                        inputRange: [0, 50],
                        outputRange: [0, 50 + (index * 20)],
                      })
                    }]
                  }
                ]}
              >
                <View style={[styles.featureIcon, { backgroundColor: feature.color + '20' }]}>
                  <feature.icon size={24} color={feature.color} />
                </View>
                <View style={styles.featureContent}>
                  <Text style={styles.featureTitle}>{feature.title}</Text>
                  <Text style={styles.featureDescription}>{feature.description}</Text>
                </View>
              </Animated.View>
            ))}
          </View>

          {/* CTA Button */}
          <Animated.View
            style={[
              styles.ctaContainer,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <TouchableOpacity
              style={styles.ctaButton}
              onPress={onGetStarted}
              activeOpacity={0.9}
            >
              <LinearGradient
                colors={['rgba(255, 255, 255, 0.9)', 'rgba(255, 255, 255, 0.8)']}
                style={styles.ctaGradient}
              >
                <Text style={styles.ctaText}>Get Started</Text>
                <ChevronRight size={20} color={colors.primary[600]} />
              </LinearGradient>
            </TouchableOpacity>
            
            <Text style={styles.footerText}>
              Join thousands of students achieving their HSC Science goals
            </Text>
          </Animated.View>
        </Animated.View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  backgroundDecorations: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  floatingElement: {
    position: 'absolute',
  },
  element1: {
    top: height * 0.15,
    right: width * 0.1,
  },
  element2: {
    top: height * 0.3,
    left: width * 0.05,
  },
  element3: {
    bottom: height * 0.25,
    right: width * 0.15,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing['2xl'],
    justifyContent: 'space-between',
    paddingTop: spacing['4xl'],
    paddingBottom: spacing['3xl'],
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing['4xl'],
  },
  logoContainer: {
    marginBottom: spacing['2xl'],
  },
  logoGradient: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  title: {
    fontSize: typography.sizes['6xl'],
    fontFamily: typography.fonts.headingBold,
    color: colors.text.inverse,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  subtitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.regular,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: typography.lineHeights.relaxed * typography.sizes.lg,
    paddingHorizontal: spacing.lg,
  },
  featuresContainer: {
    gap: spacing.lg,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: borderRadius.xl,
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.lg,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.inverse,
    marginBottom: spacing.xs,
  },
  featureDescription: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: typography.lineHeights.normal * typography.sizes.sm,
  },
  ctaContainer: {
    alignItems: 'center',
    gap: spacing.lg,
  },
  ctaButton: {
    width: '100%',
    borderRadius: borderRadius.xl,
    overflow: 'hidden',
  },
  ctaGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing['2xl'],
    gap: spacing.sm,
  },
  ctaText: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.semiBold,
    color: colors.primary[600],
  },
  footerText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
});
