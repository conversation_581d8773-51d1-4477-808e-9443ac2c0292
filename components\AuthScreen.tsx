import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Animated,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { BookOpen, Mail, Lock, User, Eye, EyeOff, ArrowLeft, Sparkles } from 'lucide-react-native';
import { useAuth } from '@/hooks/useAuth';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Loading from '@/components/ui/Loading';
import { colors, spacing, borderRadius, typography, shadows } from '@/constants/theme';

import WelcomeScreen from './WelcomeScreen';

export default function AuthScreen() {
  const [showWelcome, setShowWelcome] = useState(true);
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const { signIn, signUp } = useAuth();

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    if (!showWelcome) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [showWelcome]);

  const handleAuth = async () => {
    if (!email || !password || (!isLogin && !fullName)) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    try {
      if (isLogin) {
        const { error } = await signIn(email, password);
        if (error) throw error;
      } else {
        const { error } = await signUp(email, password, fullName);
        if (error) throw error;
        Alert.alert(
          'Success! 🎉',
          'Account created successfully! Please check your email to verify your account.',
          [{ text: 'OK', style: 'default' }]
        );
      }
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  if (showWelcome) {
    return <WelcomeScreen onGetStarted={() => setShowWelcome(false)} />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />
      <LinearGradient
        colors={colors.gradients.primary}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Background Decorations */}
        <View style={styles.backgroundDecorations}>
          <Animated.View style={[styles.floatingElement, styles.element1, { opacity: fadeAnim }]}>
            <Sparkles size={20} color="rgba(255, 255, 255, 0.3)" />
          </Animated.View>
          <Animated.View style={[styles.floatingElement, styles.element2, { opacity: fadeAnim }]}>
            <BookOpen size={24} color="rgba(255, 255, 255, 0.2)" />
          </Animated.View>
        </View>

        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
            {/* Back Button */}
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => setShowWelcome(true)}
            >
              <ArrowLeft size={24} color={colors.text.inverse} />
            </TouchableOpacity>

            {/* Header */}
            <Animated.View
              style={[
                styles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }]
                }
              ]}
            >
              <View style={styles.logoContainer}>
                <LinearGradient
                  colors={['rgba(255, 255, 255, 0.3)', 'rgba(255, 255, 255, 0.1)']}
                  style={styles.logoGradient}
                >
                  <BookOpen size={40} color={colors.text.inverse} />
                </LinearGradient>
              </View>
              <Text style={styles.title}>Welcome Back</Text>
              <Text style={styles.subtitle}>
                Sign in to continue your learning journey
              </Text>
            </Animated.View>

            {/* Auth Form */}
            <Animated.View
              style={[
                styles.formContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }]
                }
              ]}
            >
              <Card variant="glass" style={styles.formCard}>
                <View style={styles.tabContainer}>
                  <TouchableOpacity
                    style={[styles.tab, isLogin && styles.activeTab]}
                    onPress={() => setIsLogin(true)}
                  >
                    <Text style={[styles.tabText, isLogin && styles.activeTabText]}>
                      Sign In
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.tab, !isLogin && styles.activeTab]}
                    onPress={() => setIsLogin(false)}
                  >
                    <Text style={[styles.tabText, !isLogin && styles.activeTabText]}>
                      Sign Up
                    </Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.inputsContainer}>
                  {!isLogin && (
                    <View style={styles.inputContainer}>
                      <View style={styles.inputIconContainer}>
                        <User size={20} color={colors.text.secondary} />
                      </View>
                      <TextInput
                        style={styles.input}
                        placeholder="Full Name"
                        value={fullName}
                        onChangeText={setFullName}
                        placeholderTextColor={colors.text.tertiary}
                      />
                    </View>
                  )}

                  <View style={styles.inputContainer}>
                    <View style={styles.inputIconContainer}>
                      <Mail size={20} color={colors.text.secondary} />
                    </View>
                    <TextInput
                      style={styles.input}
                      placeholder="Email Address"
                      value={email}
                      onChangeText={setEmail}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      placeholderTextColor={colors.text.tertiary}
                    />
                  </View>

                  <View style={styles.inputContainer}>
                    <View style={styles.inputIconContainer}>
                      <Lock size={20} color={colors.text.secondary} />
                    </View>
                    <TextInput
                      style={styles.input}
                      placeholder="Password"
                      value={password}
                      onChangeText={setPassword}
                      secureTextEntry={!showPassword}
                      placeholderTextColor={colors.text.tertiary}
                    />
                    <TouchableOpacity
                      style={styles.eyeIcon}
                      onPress={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff size={20} color={colors.text.secondary} />
                      ) : (
                        <Eye size={20} color={colors.text.secondary} />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>

                <Button
                  title={loading ? '' : isLogin ? 'Sign In' : 'Create Account'}
                  onPress={handleAuth}
                  disabled={loading}
                  variant="gradient"
                  size="lg"
                  fullWidth
                  style={styles.authButton}
                  icon={loading ? <Loading variant="spinner" size="sm" color={colors.text.inverse} /> : undefined}
                />

                {isLogin && (
                  <TouchableOpacity style={styles.forgotPassword}>
                    <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
                  </TouchableOpacity>
                )}
              </Card>
            </Animated.View>

            {/* Features */}
            <Animated.View
              style={[
                styles.featuresContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }]
                }
              ]}
            >
              <Card variant="flat" style={styles.featuresCard}>
                <Text style={styles.featuresTitle}>Why choose HSC Science Hub?</Text>
                <View style={styles.featuresList}>
                  <View style={styles.featureItem}>
                    <View style={styles.featureBullet} />
                    <Text style={styles.featureText}>AI-powered personalized tutoring</Text>
                  </View>
                  <View style={styles.featureItem}>
                    <View style={styles.featureBullet} />
                    <Text style={styles.featureText}>Interactive practice tests & quizzes</Text>
                  </View>
                  <View style={styles.featureItem}>
                    <View style={styles.featureBullet} />
                    <Text style={styles.featureText}>Real-time progress tracking</Text>
                  </View>
                  <View style={styles.featureItem}>
                    <View style={styles.featureBullet} />
                    <Text style={styles.featureText}>Comprehensive study materials</Text>
                  </View>
                </View>
              </Card>
            </Animated.View>
          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  backgroundDecorations: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  floatingElement: {
    position: 'absolute',
  },
  element1: {
    top: '15%',
    right: '10%',
  },
  element2: {
    bottom: '25%',
    left: '8%',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing['2xl'],
  },
  backButton: {
    position: 'absolute',
    top: spacing.lg,
    left: spacing.xl,
    zIndex: 10,
    width: 44,
    height: 44,
    borderRadius: borderRadius.xl,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing['4xl'],
    marginTop: spacing['4xl'],
  },
  logoContainer: {
    marginBottom: spacing.xl,
  },
  logoGradient: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  title: {
    fontSize: typography.sizes['5xl'],
    fontFamily: typography.fonts.headingBold,
    color: colors.text.inverse,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.regular,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: typography.lineHeights.relaxed * typography.sizes.lg,
  },
  formContainer: {
    marginBottom: spacing['3xl'],
  },
  formCard: {
    padding: spacing['2xl'],
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: colors.background.tertiary,
    borderRadius: borderRadius.xl,
    padding: spacing.xs,
    marginBottom: spacing['2xl'],
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
    borderRadius: borderRadius.lg,
  },
  activeTab: {
    backgroundColor: colors.background.primary,
    ...shadows.sm,
  },
  tabText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.medium,
    color: colors.text.secondary,
  },
  activeTabText: {
    color: colors.text.primary,
    fontFamily: typography.fonts.semiBold,
  },
  inputsContainer: {
    gap: spacing.lg,
    marginBottom: spacing['2xl'],
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.xl,
    borderWidth: 1,
    borderColor: colors.border.light,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xs,
  },
  inputIconContainer: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.background.tertiary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  input: {
    flex: 1,
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.regular,
    color: colors.text.primary,
    paddingVertical: spacing.md,
  },
  eyeIcon: {
    padding: spacing.sm,
  },
  authButton: {
    marginTop: spacing.lg,
  },
  forgotPassword: {
    alignItems: 'center',
    marginTop: spacing.lg,
  },
  forgotPasswordText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.medium,
    color: colors.primary[600],
  },
  featuresContainer: {
    marginTop: spacing.xl,
  },
  featuresCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    padding: spacing.xl,
  },
  featuresTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.inverse,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  featuresList: {
    gap: spacing.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  featureBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.accent.yellow,
  },
  featureText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: 'rgba(255, 255, 255, 0.9)',
    flex: 1,
  },
});