import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Switch, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { User, Settings, BookOpen, Trophy, Target, Clock, Bell, Shield, CircleHelp as HelpCircle, LogOut, ChevronRight, CreditCard as Edit, Star, TrendingUp, Award } from 'lucide-react-native';
import { useAuth } from '@/hooks/useAuth';
import { useStudyProgress } from '@/hooks/useStudyProgress';
import { useQuizResults } from '@/hooks/useQuizResults';
import { supabase } from '@/lib/supabase';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { colors, spacing, borderRadius, typography } from '@/constants/theme';

export default function ProfileScreen() {
  const { user, signOut } = useAuth();
  const { progress } = useStudyProgress();
  const { results } = useQuizResults();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = useState(false);
  const [userStats, setUserStats] = useState({
    totalStudyTime: '0h 0m',
    quizzesCompleted: 0,
    averageScore: 0,
    currentStreak: 0,
  });

  useEffect(() => {
    calculateUserStats();
  }, [progress, results, user]);

  const calculateUserStats = async () => {
    if (!user) return;

    try {
      // Get study sessions for total time
      const { data: sessions } = await supabase
        .from('study_sessions')
        .select('duration')
        .eq('user_id', user.id);

      const totalMinutes = sessions?.reduce((sum, session) => sum + session.duration, 0) || 0;
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;

      // Calculate average score
      const averageScore = results.length > 0
        ? Math.round(results.reduce((sum, result) => sum + (result.score / result.total_questions * 100), 0) / results.length)
        : 0;

      setUserStats({
        totalStudyTime: `${hours}h ${minutes}m`,
        quizzesCompleted: results.length,
        averageScore,
        currentStreak: 15, // Placeholder - would need more complex logic
      });
    } catch (error) {
      console.error('Error calculating user stats:', error);
    }
  };

  const subjects = [
    { name: 'Physics', progress: 75, color: '#3B82F6' },
    { name: 'Chemistry', progress: 82, color: '#10B981' },
    { name: 'Mathematics', progress: 68, color: '#F59E0B' },
    { name: 'Biology', progress: 71, color: '#8B5CF6' },
    { name: 'English', progress: 89, color: '#06B6D4' },
  ];

  const recentAchievements = [
    { title: 'Chemistry Expert', date: '2 days ago', icon: '🧪' },
    { title: '100 Day Streak', date: '1 week ago', icon: '🔥' },
    { title: 'Math Wizard', date: '2 weeks ago', icon: '📐' },
  ];

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            const { error } = await signOut();
            if (error) {
              Alert.alert('Error', error.message);
            }
          },
        },
      ]
    );
  };

  const settingsOptions = [
    {
      id: 'notifications',
      title: 'Push Notifications',
      subtitle: 'Get study reminders and updates',
      icon: Bell,
      type: 'toggle',
      value: notificationsEnabled,
      onToggle: setNotificationsEnabled,
    },
    {
      id: 'darkmode',
      title: 'Dark Mode',
      subtitle: 'Switch to dark theme',
      icon: Shield,
      type: 'toggle',
      value: darkModeEnabled,
      onToggle: setDarkModeEnabled,
    },
    {
      id: 'help',
      title: 'Help & Support',
      subtitle: 'Get help or contact support',
      icon: HelpCircle,
      type: 'navigate',
      onPress: () => Alert.alert('Help', 'Contact <NAME_EMAIL>'),
    },
    {
      id: 'logout',
      title: 'Logout',
      subtitle: 'Sign out of your account',
      icon: LogOut,
      type: 'navigate',
      onPress: handleSignOut,
      danger: true,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <Card variant="gradient" gradientColors={colors.gradients.primary} style={styles.profileHeader}>
          <View style={styles.profileImageContainer}>
            <Image
              source={{ uri: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&fit=crop' }}
              style={styles.profileImage}
            />
            <TouchableOpacity style={styles.editButton}>
              <Edit size={16} color={colors.text.inverse} />
            </TouchableOpacity>
          </View>
          <Text style={styles.userName}>{user?.user_metadata?.full_name || 'Student'}</Text>
          <Text style={styles.userEmail}>{user?.email}</Text>
          <View style={styles.userClassBadge}>
            <Star size={16} color={colors.accent.yellow} />
            <Text style={styles.userClass}>HSC Science • Class 12</Text>
          </View>
        </Card>

        {/* Stats Overview */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>Study Overview</Text>
          <View style={styles.statsGrid}>
            <Card variant="elevated" style={styles.statCard}>
              <View style={[styles.statIcon, { backgroundColor: colors.accent.blue + '20' }]}>
                <Clock size={24} color={colors.accent.blue} />
              </View>
              <Text style={styles.statValue}>{userStats.totalStudyTime}</Text>
              <Text style={styles.statLabel}>Total Time</Text>
            </Card>

            <Card variant="elevated" style={styles.statCard}>
              <View style={[styles.statIcon, { backgroundColor: colors.accent.green + '20' }]}>
                <Target size={24} color={colors.accent.green} />
              </View>
              <Text style={styles.statValue}>{userStats.quizzesCompleted}</Text>
              <Text style={styles.statLabel}>Quizzes</Text>
            </Card>

            <Card variant="elevated" style={styles.statCard}>
              <View style={[styles.statIcon, { backgroundColor: colors.accent.yellow + '20' }]}>
                <Trophy size={24} color={colors.accent.yellow} />
              </View>
              <Text style={styles.statValue}>{userStats.averageScore}%</Text>
              <Text style={styles.statLabel}>Avg Score</Text>
            </Card>

            <Card variant="elevated" style={styles.statCard}>
              <View style={styles.streakIconContainer}>
                <Text style={styles.streakIcon}>🔥</Text>
              </View>
              <Text style={styles.statValue}>{userStats.currentStreak}</Text>
              <Text style={styles.statLabel}>Day Streak</Text>
            </Card>
          </View>
        </View>

        {/* Subject Progress */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Subject Progress</Text>
          {subjects.map((subject, index) => {
            const subjectProgress = progress.filter(p => p.subject.toLowerCase() === subject.name.toLowerCase());
            const actualProgress = subjectProgress.length > 0
              ? Math.round(subjectProgress.reduce((sum, p) => sum + p.progress, 0) / subjectProgress.length)
              : subject.progress; // Fallback to default

            return (
              <Card key={index} variant="elevated" style={styles.subjectProgressItem}>
                <View style={styles.subjectHeader}>
                  <View style={styles.subjectInfo}>
                    <View style={[styles.subjectColorDot, { backgroundColor: subject.color }]} />
                    <Text style={styles.subjectName}>{subject.name}</Text>
                  </View>
                  <View style={styles.subjectStats}>
                    <Text style={[styles.subjectProgress, { color: subject.color }]}>
                      {actualProgress}%
                    </Text>
                    <TrendingUp size={16} color={colors.status.success} />
                  </View>
                </View>
                <View style={styles.progressBarContainer}>
                  <View style={styles.progressBar}>
                    <View
                      style={[
                        styles.progressFill,
                        { width: `${actualProgress}%`, backgroundColor: subject.color }
                      ]}
                    />
                  </View>
                </View>
              </Card>
            );
          })}
        </View>

        {/* Recent Achievements */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Achievements</Text>
          {recentAchievements.map((achievement, index) => (
            <Card key={index} variant="elevated" style={styles.achievementItem}>
              <View style={styles.achievementIconContainer}>
                <Text style={styles.achievementIcon}>{achievement.icon}</Text>
              </View>
              <View style={styles.achievementContent}>
                <Text style={styles.achievementTitle}>{achievement.title}</Text>
                <Text style={styles.achievementDate}>{achievement.date}</Text>
              </View>
              <View style={styles.achievementBadge}>
                <Award size={16} color={colors.accent.yellow} />
              </View>
            </Card>
          ))}
        </View>

        {/* Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Settings</Text>
          {settingsOptions.map((option) => (
            <Card
              key={option.id}
              variant="elevated"
              onPress={option.onPress}
              style={[styles.settingItem, option.danger && styles.dangerItem]}
            >
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, option.danger && styles.dangerIcon]}>
                  <option.icon
                    size={20}
                    color={option.danger ? colors.status.error : colors.text.secondary}
                  />
                </View>
                <View style={styles.settingContent}>
                  <Text style={[styles.settingTitle, option.danger && styles.dangerText]}>
                    {option.title}
                  </Text>
                  <Text style={styles.settingSubtitle}>{option.subtitle}</Text>
                </View>
              </View>

              {option.type === 'toggle' ? (
                <Switch
                  value={option.value}
                  onValueChange={option.onToggle}
                  trackColor={{ false: colors.border.medium, true: colors.primary[500] }}
                  thumbColor={colors.background.primary}
                />
              ) : (
                <ChevronRight size={20} color={colors.text.tertiary} />
              )}
            </Card>
          ))}
        </View>

        {/* App Info */}
        <View style={styles.appInfo}>
          <Text style={styles.appVersion}>HSC Science Hub v1.0.0</Text>
          <Text style={styles.appCopyright}>© 2024 Education Platform</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  profileHeader: {
    alignItems: 'center',
    marginHorizontal: spacing.lg,
    marginTop: spacing.md,
    marginBottom: spacing.xl,
    padding: spacing['3xl'],
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: spacing.xl,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  editButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  userName: {
    fontSize: typography.sizes['3xl'],
    fontFamily: typography.fonts.headingBold,
    color: colors.text.inverse,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  userEmail: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.regular,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  userClassBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.xl,
    gap: spacing.sm,
  },
  userClass: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.inverse,
  },
  statsContainer: {
    paddingHorizontal: spacing.xl,
    marginBottom: spacing['3xl'],
  },
  sectionTitle: {
    fontSize: typography.sizes['2xl'],
    fontFamily: typography.fonts.headingSemiBold,
    color: colors.text.primary,
    marginBottom: spacing.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: spacing.md,
  },
  statCard: {
    width: '48%',
    alignItems: 'center',
    padding: spacing.lg,
    minHeight: 120,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.xl,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.md,
  },
  streakIconContainer: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.xl,
    backgroundColor: colors.accent.orange + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.md,
  },
  streakIcon: {
    fontSize: 24,
  },
  statValue: {
    fontSize: typography.sizes['2xl'],
    fontFamily: typography.fonts.headingBold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.medium,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: spacing.xl,
    marginBottom: spacing['3xl'],
  },
  subjectProgressItem: {
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  subjectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  subjectInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  subjectColorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: spacing.md,
  },
  subjectName: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.primary,
  },
  subjectStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  subjectProgress: {
    fontSize: typography.sizes.base,
    fontFamily: typography.fonts.bold,
  },
  progressBarContainer: {
    width: '100%',
  },
  progressBar: {
    height: 10,
    backgroundColor: colors.background.tertiary,
    borderRadius: borderRadius.full,
  },
  progressFill: {
    height: '100%',
    borderRadius: borderRadius.full,
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  achievementIconContainer: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.xl,
    backgroundColor: colors.background.tertiary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.lg,
  },
  achievementIcon: {
    fontSize: 24,
  },
  achievementContent: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  achievementDate: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.text.secondary,
  },
  achievementBadge: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.accent.yellow + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  dangerItem: {
    borderColor: colors.status.error + '30',
    borderWidth: 1,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 44,
    height: 44,
    borderRadius: borderRadius.xl,
    backgroundColor: colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.lg,
  },
  dangerIcon: {
    backgroundColor: colors.status.error + '20',
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  dangerText: {
    color: colors.status.error,
  },
  settingSubtitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.text.secondary,
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: spacing['3xl'],
    paddingHorizontal: spacing.xl,
  },
  appVersion: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.medium,
    color: colors.text.tertiary,
    marginBottom: spacing.xs,
  },
  appCopyright: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.regular,
    color: colors.text.tertiary,
    opacity: 0.7,
  },
});