import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BookOpen, Sparkles } from 'lucide-react-native';
import { colors, spacing, borderRadius, typography } from '@/constants/theme';

const { width, height } = Dimensions.get('window');

export default function SplashScreen() {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Logo animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Continuous rotation for sparkles
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 3000,
        useNativeDriver: true,
      })
    ).start();

    // Pulse animation for logo
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <LinearGradient
        colors={colors.gradients.primary}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Background Decorations */}
        <View style={styles.backgroundDecorations}>
          <Animated.View 
            style={[
              styles.floatingElement,
              styles.element1,
              { 
                opacity: fadeAnim,
                transform: [{ rotate: spin }]
              }
            ]}
          >
            <Sparkles size={32} color="rgba(255, 255, 255, 0.3)" />
          </Animated.View>
          
          <Animated.View 
            style={[
              styles.floatingElement,
              styles.element2,
              { 
                opacity: fadeAnim,
                transform: [{ 
                  rotate: rotateAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '-360deg'],
                  })
                }]
              }
            ]}
          >
            <Sparkles size={24} color="rgba(255, 255, 255, 0.2)" />
          </Animated.View>
          
          <Animated.View 
            style={[
              styles.floatingElement,
              styles.element3,
              { 
                opacity: fadeAnim,
                transform: [{ rotate: spin }]
              }
            ]}
          >
            <Sparkles size={28} color="rgba(255, 255, 255, 0.25)" />
          </Animated.View>
        </View>

        {/* Main Content */}
        <Animated.View 
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [
                { scale: scaleAnim }
              ]
            }
          ]}
        >
          {/* Logo */}
          <Animated.View 
            style={[
              styles.logoContainer,
              {
                transform: [{ scale: pulseAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['rgba(255, 255, 255, 0.3)', 'rgba(255, 255, 255, 0.1)']}
              style={styles.logoGradient}
            >
              <BookOpen size={64} color={colors.text.inverse} />
            </LinearGradient>
          </Animated.View>

          {/* App Name */}
          <Text style={styles.appName}>HSC Science Hub</Text>
          <Text style={styles.tagline}>AI-Powered Learning Platform</Text>

          {/* Loading Indicator */}
          <Animated.View 
            style={[
              styles.loadingContainer,
              { opacity: fadeAnim }
            ]}
          >
            <View style={styles.loadingBar}>
              <Animated.View 
                style={[
                  styles.loadingFill,
                  {
                    transform: [{
                      translateX: rotateAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [-100, 100],
                      })
                    }]
                  }
                ]}
              />
            </View>
            <Text style={styles.loadingText}>Loading...</Text>
          </Animated.View>
        </Animated.View>

        {/* Footer */}
        <Animated.View 
          style={[
            styles.footer,
            { opacity: fadeAnim }
          ]}
        >
          <Text style={styles.footerText}>
            Empowering Maharashtra HSC Science Students
          </Text>
        </Animated.View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  backgroundDecorations: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  floatingElement: {
    position: 'absolute',
  },
  element1: {
    top: height * 0.15,
    right: width * 0.1,
  },
  element2: {
    top: height * 0.25,
    left: width * 0.05,
  },
  element3: {
    bottom: height * 0.3,
    right: width * 0.15,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing['2xl'],
  },
  logoContainer: {
    marginBottom: spacing['3xl'],
  },
  logoGradient: {
    width: 140,
    height: 140,
    borderRadius: 70,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  appName: {
    fontSize: typography.sizes['7xl'],
    fontFamily: typography.fonts.headingBold,
    color: colors.text.inverse,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  tagline: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fonts.regular,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: spacing['4xl'],
  },
  loadingContainer: {
    alignItems: 'center',
    gap: spacing.lg,
  },
  loadingBar: {
    width: 200,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: borderRadius.full,
    overflow: 'hidden',
  },
  loadingFill: {
    width: 100,
    height: '100%',
    backgroundColor: colors.accent.yellow,
    borderRadius: borderRadius.full,
  },
  loadingText: {
    fontSize: typography.sizes.base,
    fontFamily: typography.fonts.medium,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  footer: {
    position: 'absolute',
    bottom: spacing['4xl'],
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  footerText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
});
