import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Easing,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, borderRadius, typography } from '@/constants/theme';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  text?: string;
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton';
}

export default function Loading({
  size = 'md',
  color = colors.primary[500],
  text,
  variant = 'spinner',
}: LoadingProps) {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const pulseValue = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (variant === 'spinner') {
      const spinAnimation = Animated.loop(
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      );
      spinAnimation.start();
      return () => spinAnimation.stop();
    }

    if (variant === 'pulse') {
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseValue, {
            toValue: 1.2,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseValue, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();
      return () => pulseAnimation.stop();
    }
  }, [variant]);

  const getSize = () => {
    switch (size) {
      case 'sm': return 24;
      case 'lg': return 48;
      default: return 32;
    }
  };

  const spin = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  if (variant === 'spinner') {
    return (
      <View style={styles.container}>
        <Animated.View
          style={[
            styles.spinner,
            {
              width: getSize(),
              height: getSize(),
              borderColor: `${color}20`,
              borderTopColor: color,
              transform: [{ rotate: spin }],
            },
          ]}
        />
        {text && <Text style={[styles.text, { color }]}>{text}</Text>}
      </View>
    );
  }

  if (variant === 'dots') {
    return (
      <View style={styles.container}>
        <View style={styles.dotsContainer}>
          {[0, 1, 2].map((index) => (
            <DotLoader key={index} delay={index * 200} color={color} size={getSize() / 4} />
          ))}
        </View>
        {text && <Text style={[styles.text, { color }]}>{text}</Text>}
      </View>
    );
  }

  if (variant === 'pulse') {
    return (
      <View style={styles.container}>
        <Animated.View
          style={[
            styles.pulse,
            {
              width: getSize(),
              height: getSize(),
              backgroundColor: color,
              transform: [{ scale: pulseValue }],
            },
          ]}
        />
        {text && <Text style={[styles.text, { color }]}>{text}</Text>}
      </View>
    );
  }

  if (variant === 'skeleton') {
    return <SkeletonLoader />;
  }

  return null;
}

function DotLoader({ delay, color, size }: { delay: number; color: string; size: number }) {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.delay(delay),
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ])
    );
    animation.start();
    return () => animation.stop();
  }, [delay]);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 1],
  });

  return (
    <Animated.View
      style={[
        styles.dot,
        {
          width: size,
          height: size,
          backgroundColor: color,
          opacity,
        },
      ]}
    />
  );
}

function SkeletonLoader() {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: false,
      })
    );
    animation.start();
    return () => animation.stop();
  }, []);

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-100, 100],
  });

  return (
    <View style={styles.skeletonContainer}>
      <View style={styles.skeletonLine}>
        <Animated.View
          style={[
            styles.skeletonShimmer,
            {
              transform: [{ translateX }],
            },
          ]}
        />
      </View>
      <View style={[styles.skeletonLine, { width: '80%' }]}>
        <Animated.View
          style={[
            styles.skeletonShimmer,
            {
              transform: [{ translateX }],
            },
          ]}
        />
      </View>
      <View style={[styles.skeletonLine, { width: '60%' }]}>
        <Animated.View
          style={[
            styles.skeletonShimmer,
            {
              transform: [{ translateX }],
            },
          ]}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.lg,
  },
  spinner: {
    borderWidth: 3,
    borderRadius: 50,
  },
  text: {
    marginTop: spacing.md,
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.medium,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  dot: {
    borderRadius: 50,
  },
  pulse: {
    borderRadius: 50,
  },
  skeletonContainer: {
    padding: spacing.lg,
    gap: spacing.md,
  },
  skeletonLine: {
    height: 16,
    backgroundColor: colors.background.tertiary,
    borderRadius: borderRadius.sm,
    overflow: 'hidden',
  },
  skeletonShimmer: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.background.primary,
    opacity: 0.5,
  },
});
