import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  Text,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { colors, spacing, borderRadius, shadows } from '@/constants/theme';

interface CardProps {
  children: React.ReactNode;
  variant?: 'elevated' | 'flat' | 'glass' | 'gradient';
  onPress?: () => void;
  style?: ViewStyle;
  padding?: keyof typeof spacing;
  margin?: keyof typeof spacing;
  borderRadius?: keyof typeof borderRadius;
  hapticFeedback?: boolean;
  gradientColors?: string[];
}

export default function Card({
  children,
  variant = 'elevated',
  onPress,
  style,
  padding = 'lg',
  margin,
  borderRadius: borderRadiusProp = 'xl',
  hapticFeedback = true,
  gradientColors = colors.gradients.primary,
}: CardProps) {
  const handlePress = () => {
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    onPress?.();
  };

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: borderRadius[borderRadiusProp],
      padding: spacing[padding],
    };

    if (margin) {
      baseStyle.margin = spacing[margin];
    }

    switch (variant) {
      case 'flat':
        return {
          ...baseStyle,
          backgroundColor: colors.background.card,
          borderWidth: 1,
          borderColor: colors.border.light,
        };

      case 'glass':
        return {
          ...baseStyle,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.2)',
          ...shadows.md,
        };

      case 'gradient':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          ...shadows.lg,
        };

      default: // elevated
        return {
          ...baseStyle,
          backgroundColor: colors.background.card,
          ...shadows.lg,
        };
    }
  };

  const CardContent = ({ children }: { children: React.ReactNode }) => (
    <View style={[getCardStyle(), style]}>
      {children}
    </View>
  );

  if (variant === 'gradient') {
    const Component = onPress ? TouchableOpacity : View;
    return (
      <Component
        onPress={onPress ? handlePress : undefined}
        activeOpacity={onPress ? 0.9 : 1}
        style={style}
      >
        <LinearGradient
          colors={gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[getCardStyle(), { backgroundColor: 'transparent' }]}
        >
          {children}
        </LinearGradient>
      </Component>
    );
  }

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={handlePress}
        activeOpacity={0.95}
        style={[getCardStyle(), style]}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={[getCardStyle(), style]}>
      {children}
    </View>
  );
}

// Specialized Card Components
export function StatsCard({
  icon,
  value,
  label,
  color = colors.primary[500],
  onPress,
  style,
}: {
  icon: React.ReactNode;
  value: string | number;
  label: string;
  color?: string;
  onPress?: () => void;
  style?: ViewStyle;
}) {
  return (
    <Card
      variant="elevated"
      onPress={onPress}
      style={[styles.statsCard, style]}
    >
      <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>
        {icon}
      </View>
      <View style={styles.statsContent}>
        <Text style={[styles.statsValue, { color }]}>{value}</Text>
        <Text style={styles.statsLabel}>{label}</Text>
      </View>
    </Card>
  );
}

export function ProgressCard({
  title,
  subtitle,
  progress,
  color = colors.primary[500],
  onPress,
  style,
}: {
  title: string;
  subtitle?: string;
  progress: number;
  color?: string;
  onPress?: () => void;
  style?: ViewStyle;
}) {
  return (
    <Card
      variant="elevated"
      onPress={onPress}
      style={[styles.progressCard, style]}
    >
      <View style={styles.progressHeader}>
        <Text style={styles.progressTitle}>{title}</Text>
        {subtitle && <Text style={styles.progressSubtitle}>{subtitle}</Text>}
      </View>
      <View style={styles.progressBarContainer}>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              { width: `${progress}%`, backgroundColor: color },
            ]}
          />
        </View>
        <Text style={[styles.progressText, { color }]}>{progress}%</Text>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  statsCard: {
    alignItems: 'center',
    minHeight: 100,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  statsContent: {
    alignItems: 'center',
  },
  statsValue: {
    fontSize: 20,
    fontFamily: 'Poppins-Bold',
    marginBottom: 2,
  },
  statsLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.text.secondary,
  },
  progressCard: {
    padding: spacing.lg,
  },
  progressHeader: {
    marginBottom: spacing.md,
  },
  progressTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.text.primary,
  },
  progressSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.text.secondary,
    marginTop: 2,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: colors.background.tertiary,
    borderRadius: borderRadius.full,
    marginRight: spacing.md,
  },
  progressFill: {
    height: '100%',
    borderRadius: borderRadius.full,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
});
