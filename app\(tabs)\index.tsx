import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, RefreshControl, Animated } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { BookOpen, Target, TrendingUp, Clock, Play, Award, Flame, Bot, ChevronRight } from 'lucide-react-native';
import { router } from 'expo-router';
import { useAuth } from '@/hooks/useAuth';
import { useStudyProgress } from '@/hooks/useStudyProgress';
import { useQuizResults } from '@/hooks/useQuizResults';
import { supabase } from '@/lib/supabase';
import Card, { StatsCard } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { colors, spacing, borderRadius, shadows, typography } from '@/constants/theme';

interface StudyStats {
  totalStudyTime: string;
  completedTopics: number;
  currentStreak: number;
  averageScore: number;
}

export default function HomeScreen() {
  const { user } = useAuth();
  const { progress } = useStudyProgress();
  const { results } = useQuizResults();
  const [stats, setStats] = useState<StudyStats>({
    totalStudyTime: '0h 0m',
    completedTopics: 0,
    currentStreak: 0,
    averageScore: 0,
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    calculateStats();
  }, [progress, results]);

  const calculateStats = async () => {
    if (!user) return;

    try {
      // Get study sessions for total time
      const { data: sessions } = await supabase
        .from('study_sessions')
        .select('duration')
        .eq('user_id', user.id);

      const totalMinutes = sessions?.reduce((sum, session) => sum + session.duration, 0) || 0;
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;

      // Calculate completed topics
      const completedTopics = progress.filter(p => p.completed).length;

      // Calculate average score
      const averageScore = results.length > 0
        ? Math.round(results.reduce((sum, result) => sum + (result.score / result.total_questions * 100), 0) / results.length)
        : 0;

      // Calculate streak (simplified - consecutive days with activity)
      const currentStreak = 7; // Placeholder - would need more complex logic

      setStats({
        totalStudyTime: `${hours}h ${minutes}m`,
        completedTopics,
        currentStreak,
        averageScore,
      });
    } catch (error) {
      console.error('Error calculating stats:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await calculateStats();
    setRefreshing(false);
  };

  const upcomingTopics = [
    { subject: 'Physics', topic: 'Rotational Dynamics', time: '10:00 AM', color: '#3B82F6' },
    { subject: 'Chemistry', topic: 'p-Block Elements', time: '2:00 PM', color: '#10B981' },
    { subject: 'Mathematics', topic: 'Integration', time: '4:30 PM', color: '#F59E0B' },
  ];

  const recentAchievements = [
    { title: 'Physics Master', description: 'Completed 10 physics topics', icon: '🏆' },
    { title: 'Study Streak', description: `${stats.currentStreak} days in a row`, icon: '🔥' },
    { title: 'Quiz Champion', description: `${stats.averageScore}% average score`, icon: '⭐' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.greeting}>Good Morning! 👋</Text>
            <Text style={styles.subtitle}>Ready to ace your HSC Science?</Text>
          </View>
          <TouchableOpacity style={styles.profileButton} onPress={() => router.push('/(tabs)/profile')}>
            <Image
              source={{ uri: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop' }}
              style={styles.profileImage}
            />
            <View style={styles.onlineIndicator} />
          </TouchableOpacity>
        </View>

        {/* Today's Stats */}
        <Card
          variant="gradient"
          gradientColors={colors.gradients.primary}
          style={styles.statsCard}
        >
          <Text style={styles.statsTitle}>Today's Progress</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <View style={styles.statIcon}>
                <Clock size={20} color="#FFFFFF" />
              </View>
              <Text style={styles.statValue}>{stats.totalStudyTime}</Text>
              <Text style={styles.statLabel}>Study Time</Text>
            </View>
            <View style={styles.statItem}>
              <View style={styles.statIcon}>
                <Target size={20} color="#FFFFFF" />
              </View>
              <Text style={styles.statValue}>{stats.completedTopics}</Text>
              <Text style={styles.statLabel}>Completed</Text>
            </View>
            <View style={styles.statItem}>
              <View style={styles.statIcon}>
                <Flame size={20} color="#FFFFFF" />
              </View>
              <Text style={styles.statValue}>{stats.currentStreak}</Text>
              <Text style={styles.statLabel}>Day Streak</Text>
            </View>
          </View>
        </Card>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionGrid}>
            <Card
              variant="elevated"
              onPress={() => router.push('/(tabs)/subjects')}
              style={[styles.actionCard, { backgroundColor: colors.accent.blue + '10' }]}
            >
              <View style={[styles.actionIcon, { backgroundColor: colors.accent.blue + '20' }]}>
                <BookOpen size={28} color={colors.accent.blue} />
              </View>
              <Text style={[styles.actionText, { color: colors.accent.blue }]}>Study Now</Text>
              <Text style={styles.actionSubtext}>Continue learning</Text>
            </Card>

            <Card
              variant="elevated"
              onPress={() => router.push('/(tabs)/practice')}
              style={[styles.actionCard, { backgroundColor: colors.accent.green + '10' }]}
            >
              <View style={[styles.actionIcon, { backgroundColor: colors.accent.green + '20' }]}>
                <Target size={28} color={colors.accent.green} />
              </View>
              <Text style={[styles.actionText, { color: colors.accent.green }]}>Take Quiz</Text>
              <Text style={styles.actionSubtext}>Test knowledge</Text>
            </Card>

            <Card
              variant="elevated"
              onPress={() => router.push('/(tabs)/ai-tutor')}
              style={[styles.actionCard, { backgroundColor: colors.accent.yellow + '10' }]}
            >
              <View style={[styles.actionIcon, { backgroundColor: colors.accent.yellow + '20' }]}>
                <Bot size={28} color={colors.accent.yellow} />
              </View>
              <Text style={[styles.actionText, { color: colors.accent.yellow }]}>AI Tutor</Text>
              <Text style={styles.actionSubtext}>Get help</Text>
            </Card>
          </View>
        </View>

        {/* Upcoming Study Schedule */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Today's Schedule</Text>
            <TouchableOpacity style={styles.seeAllButton}>
              <Text style={styles.seeAllText}>See All</Text>
              <ChevronRight size={16} color={colors.primary[500]} />
            </TouchableOpacity>
          </View>
          {upcomingTopics.map((item, index) => (
            <Card
              key={index}
              variant="elevated"
              onPress={() => {}}
              style={styles.scheduleCard}
            >
              <View style={styles.scheduleContent}>
                <View style={styles.scheduleLeft}>
                  <View style={[styles.subjectDot, { backgroundColor: item.color }]} />
                  <View style={styles.scheduleInfo}>
                    <Text style={styles.scheduleSubject}>{item.subject}</Text>
                    <Text style={styles.scheduleTopic}>{item.topic}</Text>
                  </View>
                </View>
                <View style={styles.scheduleRight}>
                  <Text style={styles.scheduleTime}>{item.time}</Text>
                  <View style={[styles.statusBadge, { backgroundColor: item.color + '20' }]}>
                    <Text style={[styles.statusText, { color: item.color }]}>Upcoming</Text>
                  </View>
                </View>
              </View>
            </Card>
          ))}
        </View>

        {/* Recent Achievements */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Achievements</Text>
            <TouchableOpacity style={styles.seeAllButton}>
              <Text style={styles.seeAllText}>View All</Text>
              <ChevronRight size={16} color={colors.primary[500]} />
            </TouchableOpacity>
          </View>
          {recentAchievements.map((achievement, index) => (
            <Card
              key={index}
              variant="elevated"
              onPress={() => {}}
              style={styles.achievementCard}
            >
              <View style={styles.achievementContent}>
                <View style={styles.achievementIcon}>
                  <Text style={styles.achievementEmoji}>{achievement.icon}</Text>
                </View>
                <View style={styles.achievementInfo}>
                  <Text style={styles.achievementTitle}>{achievement.title}</Text>
                  <Text style={styles.achievementDescription}>{achievement.description}</Text>
                </View>
                <View style={styles.achievementBadge}>
                  <Award size={16} color={colors.accent.yellow} />
                </View>
              </View>
            </Card>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    paddingBottom: spacing['2xl'],
  },
  headerContent: {
    flex: 1,
  },
  greeting: {
    fontSize: typography.sizes['4xl'],
    fontFamily: typography.fonts.headingBold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.regular,
    color: colors.text.secondary,
  },
  profileButton: {
    width: 56,
    height: 56,
    borderRadius: borderRadius['2xl'],
    overflow: 'hidden',
    position: 'relative',
    ...shadows.md,
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: colors.status.success,
    borderWidth: 2,
    borderColor: colors.background.primary,
  },
  statsCard: {
    marginHorizontal: spacing.xl,
    marginBottom: spacing['3xl'],
    padding: spacing['2xl'],
  },
  statsTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.inverse,
    marginBottom: spacing.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  statValue: {
    fontSize: typography.sizes['2xl'],
    fontFamily: typography.fonts.headingBold,
    color: colors.text.inverse,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.medium,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  section: {
    paddingHorizontal: spacing.xl,
    marginBottom: spacing['4xl'],
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes['2xl'],
    fontFamily: typography.fonts.headingSemiBold,
    color: colors.text.primary,
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seeAllText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.semiBold,
    color: colors.primary[500],
    marginRight: spacing.xs,
  },
  actionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.md,
  },
  actionCard: {
    flex: 1,
    alignItems: 'center',
    padding: spacing.lg,
    minHeight: 120,
  },
  actionIcon: {
    width: 56,
    height: 56,
    borderRadius: borderRadius.xl,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.md,
  },
  actionText: {
    fontSize: typography.sizes.base,
    fontFamily: typography.fonts.semiBold,
    marginBottom: spacing.xs,
  },
  actionSubtext: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.regular,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  scheduleCard: {
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  scheduleContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  scheduleLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  subjectDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: spacing.md,
  },
  scheduleInfo: {
    flex: 1,
  },
  scheduleSubject: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  scheduleTopic: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.text.secondary,
  },
  scheduleRight: {
    alignItems: 'flex-end',
  },
  scheduleTime: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.md,
  },
  statusText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.medium,
  },
  achievementCard: {
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  achievementContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.background.tertiary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.lg,
  },
  achievementEmoji: {
    fontSize: 24,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.semiBold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  achievementDescription: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.text.secondary,
  },
  achievementBadge: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.accent.yellow + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
});